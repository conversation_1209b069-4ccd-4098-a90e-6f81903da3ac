#!/usr/bin/env python3
"""
演示脚本
用于演示智能发布计划助手的核心功能（无需数据库连接）
"""

import sys
import os

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.jenkins_client import JenkinsJobGenerator

def demo_version_parsing():
    """演示版本号解析功能"""
    print("🔍 版本号解析演示")
    print("=" * 40)
    
    # 模拟解析函数
    def parse_user_input(user_input):
        import re
        patterns = [
            r'(\d+R\d+\.\d+)\s+(\w+)',
            r'版本[：:]?\s*(\d+R\d+\.\d+).*?环境[：:]?\s*(\w+)',
            r'环境[：:]?\s*(\w+).*?版本[：:]?\s*(\d+R\d+\.\d+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, user_input)
            if match:
                groups = match.groups()
                if len(groups) == 2:
                    if 'R' in groups[0]:
                        return groups[0], groups[1]
                    else:
                        return groups[1], groups[0]
        return None, None
    
    test_inputs = [
        "列出 25R1.2 Prod 的发布计划",
        "查询 25R2.0 Test 环境的部署计划",
        "环境：Staging 版本：25R1.5"
    ]
    
    for user_input in test_inputs:
        version, environment = parse_user_input(user_input)
        print(f"输入: {user_input}")
        print(f"解析结果: 版本={version}, 环境={environment}")
        print()

def demo_jenkins_job_generation():
    """演示Jenkins job生成功能"""
    print("🔧 Jenkins Job生成演示")
    print("=" * 40)
    
    generator = JenkinsJobGenerator()
    
    # 模拟部署计划
    mock_plan = [
        {
            "计划部署日期": "2025-06-22",
            "时间窗口": "17:00-19:00",
            "客户名": "多租户服务",
            "租户名": "",
            "Service名": "em",
            "是否部署PS代码": "",
            "部署顺序": 1
        },
        {
            "计划部署日期": "2025-06-22",
            "时间窗口": "17:00-19:00",
            "客户名": "EventCore",
            "租户名": "evtcore-prod",
            "Service名": "chinacrm",
            "是否部署PS代码": "",
            "部署顺序": 2
        },
        {
            "计划部署日期": "2025-06-22",
            "时间窗口": "17:00-19:00",
            "客户名": "辉瑞",
            "租户名": "pfizer-prod",
            "Service名": "rigel",
            "是否部署PS代码": "",
            "部署顺序": 3
        },
        {
            "计划部署日期": "2025-06-22",
            "时间窗口": "22:00-00:00",
            "客户名": "百济",
            "租户名": "Beigene",
            "Service名": "chinacrm",
            "是否部署PS代码": "是",
            "部署顺序": 4
        }
    ]
    
    version = "25R1.2"
    jenkins_jobs = generator.generate_jenkins_jobs(mock_plan, version)
    
    print(f"版本: {version}")
    print(f"Release分支: {generator.generate_release_branch(version)}")
    print(f"Release标签: {generator.generate_release_tag(version)}")
    print()
    
    print("生成的Jenkins Jobs:")
    for i, job in enumerate(jenkins_jobs, 1):
        print(f"\n{i}. {job['deployment_date']} | {job['time_window']} | "
              f"{job['customer_name']} | {job['tenant_name']} | {job['service_name']}")
        print(f"   Jenkins job: {job['job_name']}")
        print("   传入参数:")
        for key, value in job['parameters'].items():
            print(f"     - {key}: {value}")

def demo_deployment_plan_format():
    """演示发布计划格式化"""
    print("📋 发布计划格式化演示")
    print("=" * 40)
    
    # 模拟发布计划数据
    deployment_plan = [
        {
            "计划部署日期": "2025-06-22",
            "时间窗口": "17:00-19:00",
            "客户名": "多租户服务",
            "租户名": "",
            "Service名": "em",
            "是否部署PS代码": ""
        },
        {
            "计划部署日期": "2025-06-22",
            "时间窗口": "17:00-19:00",
            "客户名": "多租户服务",
            "租户名": "",
            "Service名": "openlog",
            "是否部署PS代码": ""
        },
        {
            "计划部署日期": "2025-06-22",
            "时间窗口": "17:00-19:00",
            "客户名": "EventCore",
            "租户名": "evtcore-prod",
            "Service名": "chinacrm",
            "是否部署PS代码": ""
        },
        {
            "计划部署日期": "2025-06-22",
            "时间窗口": "17:00-19:00",
            "客户名": "辉瑞",
            "租户名": "pfizer-prod",
            "Service名": "rigel",
            "是否部署PS代码": ""
        },
        {
            "计划部署日期": "2025-06-22",
            "时间窗口": "22:00-00:00",
            "客户名": "百济",
            "租户名": "Beigene",
            "Service名": "chinacrm",
            "是否部署PS代码": "是"
        }
    ]
    
    # 格式化为表格
    version = "25R1.2"
    environment = "Prod"
    
    table_lines = [
        f"#### {version} {environment}环境 发布计划",
        "",
        "| 计划部署日期 | 时间窗口 | 客户名 | 租户名 | Service名 | 是否部署PS代码 |",
        "| :--- | :--- | :--- | :--- | :--- | :--- |"
    ]
    
    for item in deployment_plan:
        row = f"| {item.get('计划部署日期', '')} | {item.get('时间窗口', '')} | " \
              f"{item.get('客户名', '')} | {item.get('租户名', '')} | " \
              f"{item.get('Service名', '')} | {item.get('是否部署PS代码', '')} |"
        table_lines.append(row)
    
    formatted_plan = "\n".join(table_lines)
    print(formatted_plan)

def main():
    """主演示函数"""
    print("🚀 智能发布计划助手 - 功能演示")
    print("=" * 50)
    print()
    
    demo_version_parsing()
    print()
    
    demo_deployment_plan_format()
    print()
    
    demo_jenkins_job_generation()
    print()
    
    print("=" * 50)
    print("🎉 演示完成！")
    print()
    print("💡 下一步:")
    print("1. 配置.env文件以连接真实的MySQL和Jenkins")
    print("2. 运行 'python run.py' 启动完整的Web界面")
    print("3. 在Web界面中测试完整的工作流程")

if __name__ == "__main__":
    main()
