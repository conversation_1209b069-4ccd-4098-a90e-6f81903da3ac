#!/usr/bin/env python3
"""
测试脚本
用于测试Agent的核心功能
"""

import sys
import os
import logging
from pathlib import Path

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.release_agent import ReleaseAgent
from src.database import ReleaseQueryService
from src.jenkins_client import JenkinsJobGenerator

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_version_parsing():
    """测试版本号解析"""
    print("🧪 测试版本号解析...")
    
    # 直接测试解析函数，避免初始化数据库连接
    from src.release_agent import ReleaseAgent
    
    test_cases = [
        ("列出 25R1.2 Prod 的发布计划", ("25R1.2", "Prod")),
        ("查询 25R2.0 Test 环境的部署计划", ("25R2.0", "Test")),
        ("版本：25R1.5 环境：Staging", ("25R1.5", "Staging")),
        ("环境：Dev 版本：25R3.1", ("25R3.1", "Dev")),
        ("无效输入", (None, None))
    ]
    
    # 使用真实的agent实例来测试解析功能
    agent = ReleaseAgent()
    
    for input_text, expected in test_cases:
        result = agent.parse_user_input(input_text)
        status = "✅" if result == expected else "❌"
        print(f"{status} 输入: '{input_text}' -> 结果: {result} (期望: {expected})")

def test_jenkins_job_generation():
    """测试Jenkins job生成"""
    print("\n🧪 测试Jenkins job生成...")
    
    generator = JenkinsJobGenerator()
    
    # 测试版本号转换
    test_versions = [
        ("25R1.2", "release/251.2", "LR/251.2.0"),
        ("25R2.0", "release/252.0", "GR/252.0.0"),
        ("24R3.5", "release/243.5", "LR/243.5.0")
    ]
    
    for version, expected_branch, expected_tag in test_versions:
        branch = generator.generate_release_branch(version)
        tag = generator.generate_release_tag(version)
        
        branch_status = "✅" if branch == expected_branch else "❌"
        tag_status = "✅" if tag == expected_tag else "❌"
        
        print(f"{branch_status} 版本 {version} -> 分支: {branch} (期望: {expected_branch})")
        print(f"{tag_status} 版本 {version} -> 标签: {tag} (期望: {expected_tag})")

def test_mock_deployment_plan():
    """测试模拟部署计划"""
    print("\n🧪 测试模拟部署计划...")
    
    # 模拟部署计划数据
    mock_plan = [
        {
            "计划部署日期": "2025-06-22",
            "时间窗口": "17:00-19:00",
            "客户名": "多租户服务",
            "租户名": "",
            "Service名": "em",
            "是否部署PS代码": "",
            "部署顺序": 1
        },
        {
            "计划部署日期": "2025-06-22",
            "时间窗口": "17:00-19:00",
            "客户名": "EventCore",
            "租户名": "evtcore-prod",
            "Service名": "chinacrm",
            "是否部署PS代码": "",
            "部署顺序": 2
        },
        {
            "计划部署日期": "2025-06-22",
            "时间窗口": "17:00-19:00",
            "客户名": "辉瑞",
            "租户名": "pfizer-prod",
            "Service名": "rigel",
            "是否部署PS代码": "",
            "部署顺序": 3
        }
    ]
    
    generator = JenkinsJobGenerator()
    jenkins_jobs = generator.generate_jenkins_jobs(mock_plan, "25R1.2")
    
    print(f"✅ 生成了 {len(jenkins_jobs)} 个Jenkins jobs:")
    for i, job in enumerate(jenkins_jobs, 1):
        print(f"  {i}. {job['job_name']} - {job['customer_name']} ({job['service_name']})")
        print(f"     参数: {list(job['parameters'].keys())}")

def test_agent_workflow():
    """测试Agent工作流（模拟模式）"""
    print("\n🧪 测试Agent工作流（模拟模式）...")
    
    try:
        # 使用模拟的解析功能，避免数据库连接
        class MockAgent:
            def parse_user_input(self, user_input):
                import re
                patterns = [
                    r'(\d+R\d+\.\d+)\s+(\w+)',
                    r'版本[：:]?\s*(\d+R\d+\.\d+).*?环境[：:]?\s*(\w+)',
                    r'环境[：:]?\s*(\w+).*?版本[：:]?\s*(\d+R\d+\.\d+)',
                ]
                
                for pattern in patterns:
                    match = re.search(pattern, user_input)
                    if match:
                        groups = match.groups()
                        if len(groups) == 2:
                            if 'R' in groups[0]:
                                return groups[0], groups[1]
                            else:
                                return groups[1], groups[0]
                return None, None
        
        agent = MockAgent()
        
        # 测试用户输入解析
        user_input = "列出 25R1.2 Prod 的发布计划"
        version, environment = agent.parse_user_input(user_input)
        
        if version and environment:
            print(f"✅ 成功解析用户输入: 版本={version}, 环境={environment}")
            
            # 注意：这里不会实际查询数据库，因为可能没有配置数据库连接
            print("⚠️  跳过数据库查询测试（需要配置数据库连接）")
            
        else:
            print("❌ 用户输入解析失败")
            
    except Exception as e:
        print(f"❌ 工作流测试失败: {e}")

def test_environment_config():
    """测试环境配置"""
    print("\n🧪 测试环境配置...")
    
    required_env_vars = [
        "MYSQL_HOST", "MYSQL_USER", "MYSQL_PASSWORD", "MYSQL_DATABASE",
        "JENKINS_URL", "JENKINS_USERNAME", "JENKINS_TOKEN"
    ]
    
    missing_vars = []
    for var in required_env_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"⚠️  缺少环境变量: {', '.join(missing_vars)}")
        print("请配置.env文件")
    else:
        print("✅ 所有环境变量已配置")

def main():
    """主测试函数"""
    print("🧪 智能发布计划助手 - 功能测试")
    print("=" * 50)
    
    # 运行各项测试
    test_environment_config()
    test_version_parsing()
    test_jenkins_job_generation()
    test_mock_deployment_plan()
    test_agent_workflow()
    
    print("\n" + "=" * 50)
    print("🎉 测试完成！")
    print("\n💡 提示:")
    print("1. 配置.env文件以启用完整功能")
    print("2. 运行 'python run.py' 启动Web界面")
    print("3. 确保MySQL和Jenkins服务可访问")

if __name__ == "__main__":
    main()
