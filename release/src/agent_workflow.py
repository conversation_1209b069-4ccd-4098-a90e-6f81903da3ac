"""
LangGraph工作流设计
设计agent工作流：获取部署计划 -> 用户review -> 确认执行 -> Jenkins job触发 -> 监控执行
"""

from typing import Dict, Any, List, Optional
from langgraph.graph import StateGraph, END
from langchain_core.messages import HumanMessage, AIMessage
from pydantic import BaseModel, Field
import logging
from enum import Enum

from .database import ReleaseQueryService
from .jenkins_client import JenkinsClient, JenkinsJobGenerator

logger = logging.getLogger(__name__)

class WorkflowState(BaseModel):
    """工作流状态"""
    # 输入参数
    version: str = ""
    environment: str = ""
    
    # 发布计划相关
    deployment_plan: Optional[List[Dict]] = None
    jenkins_jobs: Optional[List[Dict]] = None
    
    # 用户交互
    user_approved_plan: bool = False
    user_approved_execution: bool = False
    selected_jobs: Optional[List[Dict]] = None
    
    # 执行状态
    current_job_index: int = 0
    job_results: List[Dict] = Field(default_factory=list)
    execution_completed: bool = False
    
    # 消息和错误
    messages: List[str] = Field(default_factory=list)
    errors: List[str] = Field(default_factory=list)
    
    # 当前步骤
    current_step: str = "start"

class ReleaseAgentWorkflow:
    """发布代理工作流"""
    
    def __init__(self):
        self.query_service = ReleaseQueryService()
        self.jenkins_client = JenkinsClient()
        self.job_generator = JenkinsJobGenerator()
        self.graph = self._build_graph()
    
    def _build_graph(self) -> StateGraph:
        """构建工作流图"""
        workflow = StateGraph(WorkflowState)
        
        # 添加节点
        workflow.add_node("get_deployment_plan", self._get_deployment_plan)
        workflow.add_node("generate_jenkins_jobs", self._generate_jenkins_jobs)
        workflow.add_node("wait_for_plan_approval", self._wait_for_plan_approval)
        workflow.add_node("wait_for_execution_approval", self._wait_for_execution_approval)
        workflow.add_node("execute_jenkins_jobs", self._execute_jenkins_jobs)
        workflow.add_node("monitor_execution", self._monitor_execution)
        workflow.add_node("complete", self._complete)
        
        # 设置入口点
        workflow.set_entry_point("get_deployment_plan")
        
        # 添加边
        workflow.add_edge("get_deployment_plan", "generate_jenkins_jobs")
        workflow.add_edge("generate_jenkins_jobs", "wait_for_plan_approval")
        
        # 条件边：计划审批
        workflow.add_conditional_edges(
            "wait_for_plan_approval",
            self._should_proceed_to_execution,
            {
                "approved": "wait_for_execution_approval",
                "rejected": END,
                "waiting": "wait_for_plan_approval"
            }
        )
        
        # 条件边：执行审批
        workflow.add_conditional_edges(
            "wait_for_execution_approval",
            self._should_start_execution,
            {
                "approved": "execute_jenkins_jobs",
                "rejected": END,
                "waiting": "wait_for_execution_approval"
            }
        )
        
        workflow.add_edge("execute_jenkins_jobs", "monitor_execution")
        
        # 条件边：监控执行
        workflow.add_conditional_edges(
            "monitor_execution",
            self._should_continue_monitoring,
            {
                "continue": "monitor_execution",
                "complete": "complete"
            }
        )
        
        workflow.add_edge("complete", END)
        
        return workflow.compile()
    
    def _get_deployment_plan(self, state: WorkflowState) -> WorkflowState:
        """获取部署计划"""
        logger.info(f"获取部署计划: {state.version} {state.environment}")
        
        try:
            result = self.query_service.get_deployment_plan(state.version, state.environment)
            
            if result["success"]:
                state.deployment_plan = result["data"]
                state.messages.append(f"成功获取 {state.version} {state.environment}环境 的发布计划")
                state.current_step = "plan_retrieved"
            else:
                state.errors.append(result["message"])
                state.current_step = "error"
                
        except Exception as e:
            error_msg = f"获取部署计划失败: {str(e)}"
            state.errors.append(error_msg)
            state.current_step = "error"
            logger.error(error_msg)
        
        return state
    
    def _generate_jenkins_jobs(self, state: WorkflowState) -> WorkflowState:
        """生成Jenkins jobs"""
        logger.info("生成Jenkins jobs")
        
        try:
            if state.deployment_plan:
                state.jenkins_jobs = self.job_generator.generate_jenkins_jobs(
                    state.deployment_plan, state.version
                )
                state.messages.append(f"成功生成 {len(state.jenkins_jobs)} 个Jenkins jobs")
                state.current_step = "jobs_generated"
            else:
                state.errors.append("没有可用的部署计划")
                state.current_step = "error"
                
        except Exception as e:
            error_msg = f"生成Jenkins jobs失败: {str(e)}"
            state.errors.append(error_msg)
            state.current_step = "error"
            logger.error(error_msg)
        
        return state
    
    def _wait_for_plan_approval(self, state: WorkflowState) -> WorkflowState:
        """等待计划审批"""
        state.current_step = "waiting_plan_approval"
        state.messages.append("等待用户审批发布计划...")
        return state
    
    def _wait_for_execution_approval(self, state: WorkflowState) -> WorkflowState:
        """等待执行审批"""
        state.current_step = "waiting_execution_approval"
        state.messages.append("等待用户确认执行Jenkins jobs...")
        return state
    
    def _execute_jenkins_jobs(self, state: WorkflowState) -> WorkflowState:
        """执行Jenkins jobs"""
        logger.info("开始执行Jenkins jobs")
        
        try:
            if state.selected_jobs:
                jobs_to_execute = state.selected_jobs
            else:
                jobs_to_execute = state.jenkins_jobs or []
            
            state.current_step = "executing"
            state.messages.append(f"开始执行 {len(jobs_to_execute)} 个Jenkins jobs")
            
            # 这里只是标记开始执行，实际执行在monitor_execution中进行
            state.job_results = []
            state.current_job_index = 0
            
        except Exception as e:
            error_msg = f"执行Jenkins jobs失败: {str(e)}"
            state.errors.append(error_msg)
            state.current_step = "error"
            logger.error(error_msg)
        
        return state
    
    def _monitor_execution(self, state: WorkflowState) -> WorkflowState:
        """监控执行"""
        logger.info(f"监控执行进度: {state.current_job_index}")
        
        try:
            jobs_to_execute = state.selected_jobs or state.jenkins_jobs or []
            
            if state.current_job_index < len(jobs_to_execute):
                current_job = jobs_to_execute[state.current_job_index]
                
                # 触发当前job
                result = self.jenkins_client.trigger_job(
                    current_job["job_name"],
                    current_job["parameters"]
                )
                
                state.job_results.append(result)
                state.messages.append(f"Job {current_job['job_name']} 执行结果: {result.get('message', '')}")
                
                state.current_job_index += 1
                state.current_step = "monitoring"
            else:
                state.execution_completed = True
                state.current_step = "completed"
                
        except Exception as e:
            error_msg = f"监控执行失败: {str(e)}"
            state.errors.append(error_msg)
            state.current_step = "error"
            logger.error(error_msg)
        
        return state
    
    def _complete(self, state: WorkflowState) -> WorkflowState:
        """完成工作流"""
        state.current_step = "complete"
        state.messages.append("工作流执行完成")
        logger.info("工作流执行完成")
        return state
    
    def _should_proceed_to_execution(self, state: WorkflowState) -> str:
        """判断是否应该进入执行阶段"""
        if state.user_approved_plan:
            return "approved"
        elif state.current_step == "waiting_plan_approval":
            return "waiting"
        else:
            return "rejected"
    
    def _should_start_execution(self, state: WorkflowState) -> str:
        """判断是否应该开始执行"""
        if state.user_approved_execution:
            return "approved"
        elif state.current_step == "waiting_execution_approval":
            return "waiting"
        else:
            return "rejected"
    
    def _should_continue_monitoring(self, state: WorkflowState) -> str:
        """判断是否应该继续监控"""
        if state.execution_completed:
            return "complete"
        else:
            return "continue"
    
    def run(self, version: str, environment: str) -> WorkflowState:
        """运行工作流"""
        initial_state = WorkflowState(
            version=version,
            environment=environment,
            current_step="start"
        )
        
        try:
            result = self.graph.invoke(initial_state)
            return result
        except Exception as e:
            logger.error(f"工作流执行失败: {e}")
            initial_state.errors.append(f"工作流执行失败: {str(e)}")
            initial_state.current_step = "error"
            return initial_state
    
    def approve_plan(self, state: WorkflowState) -> WorkflowState:
        """用户审批发布计划"""
        state.user_approved_plan = True
        state.messages.append("用户已审批发布计划")
        return state
    
    def approve_execution(self, state: WorkflowState, selected_jobs: Optional[List[Dict]] = None) -> WorkflowState:
        """用户审批执行"""
        state.user_approved_execution = True
        if selected_jobs:
            state.selected_jobs = selected_jobs
        state.messages.append("用户已确认执行")
        return state
    
    def reject_plan(self, state: WorkflowState, reason: str = "") -> WorkflowState:
        """用户拒绝发布计划"""
        state.user_approved_plan = False
        state.messages.append(f"用户拒绝发布计划: {reason}")
        state.current_step = "rejected"
        return state
    
    def reject_execution(self, state: WorkflowState, reason: str = "") -> WorkflowState:
        """用户拒绝执行"""
        state.user_approved_execution = False
        state.messages.append(f"用户拒绝执行: {reason}")
        state.current_step = "rejected"
        return state
