"""
LLM配置管理模块
支持多个LLM供应商：OpenAI、Google Gemini、Anthropic Claude
"""

import os
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class LLMProvider(Enum):
    """LLM供应商枚举"""
    OPENAI = "openai"
    GOOGLE = "google"
    ANTHROPIC = "anthropic"

@dataclass
class LLMModel:
    """LLM模型配置"""
    name: str
    display_name: str
    provider: LLMProvider
    max_tokens: int
    supports_streaming: bool = True
    cost_per_1k_tokens: float = 0.0

class LLMConfigManager:
    """LLM配置管理器"""
    
    # 支持的模型列表
    SUPPORTED_MODELS = {
        LLMProvider.OPENAI: [
            LLMModel("gpt-4o", "GPT-4o", LLMProvider.OPENAI, 128000, True, 0.005),
            LLMModel("gpt-4o-mini", "GPT-4o Mini", LLMProvider.OPENAI, 128000, True, 0.00015),
            LLMModel("gpt-4-turbo", "GPT-4 Turbo", LLMProvider.OPENAI, 128000, True, 0.01),
            LLMModel("gpt-3.5-turbo", "GPT-3.5 Turbo", LLMProvider.OPENAI, 16385, True, 0.0005),
        ],
        LLMProvider.GOOGLE: [
            LLMModel("gemini-1.5-pro", "Gemini 1.5 Pro", LLMProvider.GOOGLE, 2097152, True, 0.00125),
            LLMModel("gemini-1.5-flash", "Gemini 1.5 Flash", LLMProvider.GOOGLE, 1048576, True, 0.000075),
            LLMModel("gemini-pro", "Gemini Pro", LLMProvider.GOOGLE, 32768, True, 0.0005),
        ],
        LLMProvider.ANTHROPIC: [
            LLMModel("claude-3-5-sonnet-20241022", "Claude 3.5 Sonnet", LLMProvider.ANTHROPIC, 200000, True, 0.003),
            LLMModel("claude-3-opus-20240229", "Claude 3 Opus", LLMProvider.ANTHROPIC, 200000, True, 0.015),
            LLMModel("claude-3-haiku-20240307", "Claude 3 Haiku", LLMProvider.ANTHROPIC, 200000, True, 0.00025),
        ]
    }
    
    def __init__(self):
        self.current_provider = LLMProvider.OPENAI
        self.current_model = "gpt-3.5-turbo"
        self.load_from_env()
    
    def load_from_env(self):
        """从环境变量加载配置"""
        provider_str = os.getenv('DEFAULT_LLM_PROVIDER', 'openai')
        try:
            self.current_provider = LLMProvider(provider_str)
        except ValueError:
            logger.warning(f"未知的LLM供应商: {provider_str}，使用默认值 openai")
            self.current_provider = LLMProvider.OPENAI
        
        self.current_model = os.getenv('DEFAULT_LLM_MODEL', 'gpt-3.5-turbo')
    
    def get_available_providers(self) -> List[Dict[str, str]]:
        """获取可用的供应商列表"""
        providers = []
        for provider in LLMProvider:
            # 检查是否有对应的API密钥
            api_key = self.get_api_key(provider)
            status = "✅ 已配置" if api_key else "❌ 未配置"
            
            providers.append({
                "value": provider.value,
                "label": self.get_provider_display_name(provider),
                "status": status,
                "configured": bool(api_key)
            })
        
        return providers
    
    def get_available_models(self, provider: LLMProvider) -> List[Dict[str, Any]]:
        """获取指定供应商的可用模型"""
        models = self.SUPPORTED_MODELS.get(provider, [])
        return [
            {
                "value": model.name,
                "label": model.display_name,
                "max_tokens": model.max_tokens,
                "cost_per_1k": model.cost_per_1k_tokens,
                "streaming": model.supports_streaming
            }
            for model in models
        ]
    
    def get_provider_display_name(self, provider: LLMProvider) -> str:
        """获取供应商显示名称"""
        names = {
            LLMProvider.OPENAI: "OpenAI",
            LLMProvider.GOOGLE: "Google Gemini",
            LLMProvider.ANTHROPIC: "Anthropic Claude"
        }
        return names.get(provider, provider.value)
    
    def get_api_key(self, provider: LLMProvider) -> Optional[str]:
        """获取指定供应商的API密钥"""
        key_mapping = {
            LLMProvider.OPENAI: "OPENAI_API_KEY",
            LLMProvider.GOOGLE: "GOOGLE_API_KEY",
            LLMProvider.ANTHROPIC: "ANTHROPIC_API_KEY"
        }
        env_key = key_mapping.get(provider)
        return os.getenv(env_key) if env_key else None
    
    def set_current_config(self, provider: str, model: str):
        """设置当前配置"""
        try:
            self.current_provider = LLMProvider(provider)
            self.current_model = model
            logger.info(f"LLM配置已更新: {provider} - {model}")
        except ValueError:
            logger.error(f"无效的供应商: {provider}")
            raise ValueError(f"不支持的LLM供应商: {provider}")
    
    def get_current_config(self) -> Dict[str, str]:
        """获取当前配置"""
        return {
            "provider": self.current_provider.value,
            "model": self.current_model,
            "provider_name": self.get_provider_display_name(self.current_provider)
        }
    
    def create_llm_instance(self, temperature: float = 0.7, max_tokens: Optional[int] = None):
        """创建LLM实例"""
        try:
            if self.current_provider == LLMProvider.OPENAI:
                return self._create_openai_llm(temperature, max_tokens)
            elif self.current_provider == LLMProvider.GOOGLE:
                return self._create_google_llm(temperature, max_tokens)
            elif self.current_provider == LLMProvider.ANTHROPIC:
                return self._create_anthropic_llm(temperature, max_tokens)
            else:
                raise ValueError(f"不支持的供应商: {self.current_provider}")
        except Exception as e:
            logger.error(f"创建LLM实例失败: {e}")
            raise
    
    def _create_openai_llm(self, temperature: float, max_tokens: Optional[int]):
        """创建OpenAI LLM实例"""
        try:
            from langchain_openai import ChatOpenAI
            
            api_key = self.get_api_key(LLMProvider.OPENAI)
            if not api_key:
                raise ValueError("OpenAI API密钥未配置")
            
            kwargs = {
                "model": self.current_model,
                "temperature": temperature,
                "api_key": api_key
            }
            
            base_url = os.getenv('OPENAI_BASE_URL')
            if base_url:
                kwargs["base_url"] = base_url
            
            if max_tokens:
                kwargs["max_tokens"] = max_tokens
            
            return ChatOpenAI(**kwargs)
            
        except ImportError:
            raise ImportError("请安装 langchain-openai: pip install langchain-openai")
    
    def _create_google_llm(self, temperature: float, max_tokens: Optional[int]):
        """创建Google Gemini LLM实例"""
        try:
            from langchain_google_genai import ChatGoogleGenerativeAI
            
            api_key = self.get_api_key(LLMProvider.GOOGLE)
            if not api_key:
                raise ValueError("Google API密钥未配置")
            
            kwargs = {
                "model": self.current_model,
                "temperature": temperature,
                "google_api_key": api_key
            }
            
            if max_tokens:
                kwargs["max_output_tokens"] = max_tokens
            
            return ChatGoogleGenerativeAI(**kwargs)
            
        except ImportError:
            raise ImportError("请安装 langchain-google-genai: pip install langchain-google-genai")
    
    def _create_anthropic_llm(self, temperature: float, max_tokens: Optional[int]):
        """创建Anthropic Claude LLM实例"""
        try:
            from langchain_anthropic import ChatAnthropic
            
            api_key = self.get_api_key(LLMProvider.ANTHROPIC)
            if not api_key:
                raise ValueError("Anthropic API密钥未配置")
            
            kwargs = {
                "model": self.current_model,
                "temperature": temperature,
                "anthropic_api_key": api_key
            }
            
            if max_tokens:
                kwargs["max_tokens"] = max_tokens
            
            return ChatAnthropic(**kwargs)
            
        except ImportError:
            raise ImportError("请安装 langchain-anthropic: pip install langchain-anthropic")
    
    def test_connection(self, provider: str, model: str) -> Dict[str, Any]:
        """测试LLM连接"""
        try:
            # 临时设置配置
            original_provider = self.current_provider
            original_model = self.current_model
            
            self.set_current_config(provider, model)
            
            # 创建LLM实例并测试
            llm = self.create_llm_instance(temperature=0.1, max_tokens=50)
            
            # 发送测试消息
            from langchain_core.messages import HumanMessage
            test_message = HumanMessage(content="请回复'连接测试成功'")
            response = llm.invoke([test_message])
            
            # 恢复原配置
            self.current_provider = original_provider
            self.current_model = original_model
            
            return {
                "success": True,
                "message": "连接测试成功",
                "response": response.content if hasattr(response, 'content') else str(response)
            }
            
        except Exception as e:
            # 恢复原配置
            self.current_provider = original_provider
            self.current_model = original_model
            
            return {
                "success": False,
                "message": f"连接测试失败: {str(e)}",
                "error": str(e)
            }

# 全局配置管理器实例
llm_config_manager = LLMConfigManager()
