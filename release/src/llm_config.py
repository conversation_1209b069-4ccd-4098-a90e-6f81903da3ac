"""
LLM配置管理模块
支持多个LLM供应商：OpenAI、Google Gemini、Anthropic Claude
"""

import os
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class LLMProvider(Enum):
    """LLM供应商枚举"""
    OPENAI = "openai"
    GOOGLE = "google"
    ANTHROPIC = "anthropic"

@dataclass
class LLMModel:
    """LLM模型配置"""
    name: str
    display_name: str
    provider: LLMProvider
    max_tokens: int
    supports_streaming: bool = True
    cost_per_1k_tokens: float = 0.0

class LLMConfigManager:
    """LLM配置管理器"""

    # 模型缓存，避免频繁API调用
    _model_cache = {}
    _cache_timestamp = {}
    CACHE_DURATION = 300  # 缓存5分钟
    
    def __init__(self):
        self.current_provider = LLMProvider.OPENAI
        self.current_model = "gpt-3.5-turbo"
        self.load_from_env()
    
    def load_from_env(self):
        """从环境变量加载配置"""
        provider_str = os.getenv('DEFAULT_LLM_PROVIDER', 'openai')
        try:
            self.current_provider = LLMProvider(provider_str)
        except ValueError:
            logger.warning(f"未知的LLM供应商: {provider_str}，使用默认值 openai")
            self.current_provider = LLMProvider.OPENAI
        
        self.current_model = os.getenv('DEFAULT_LLM_MODEL', 'gpt-3.5-turbo')
    
    def get_available_providers(self) -> List[Dict[str, str]]:
        """获取可用的供应商列表"""
        providers = []
        for provider in LLMProvider:
            # 检查是否有对应的API密钥
            api_key = self.get_api_key(provider)
            status = "✅ 已配置" if api_key else "❌ 未配置"
            
            providers.append({
                "value": provider.value,
                "label": self.get_provider_display_name(provider),
                "status": status,
                "configured": bool(api_key)
            })
        
        return providers
    
    def get_available_models(self, provider: LLMProvider, use_cache: bool = True) -> List[Dict[str, Any]]:
        """获取指定供应商的可用模型"""
        import time

        # 检查缓存
        cache_key = provider.value
        current_time = time.time()

        if (use_cache and
            cache_key in self._model_cache and
            cache_key in self._cache_timestamp and
            current_time - self._cache_timestamp[cache_key] < self.CACHE_DURATION):
            logger.info(f"使用缓存的{provider.value}模型列表")
            return self._model_cache[cache_key]

        # 实时获取模型列表
        try:
            models = self._fetch_models_from_provider(provider)

            # 更新缓存
            self._model_cache[cache_key] = models
            self._cache_timestamp[cache_key] = current_time

            logger.info(f"成功获取{provider.value}的{len(models)}个模型")
            return models

        except Exception as e:
            logger.error(f"获取{provider.value}模型列表失败: {e}")

            # 如果实时获取失败，尝试使用缓存
            if cache_key in self._model_cache:
                logger.warning(f"使用过期缓存的{provider.value}模型列表")
                return self._model_cache[cache_key]

            # 如果没有缓存，返回空列表
            return []

    def _fetch_models_from_provider(self, provider: LLMProvider) -> List[Dict[str, Any]]:
        """从供应商实时获取模型列表"""
        if provider == LLMProvider.OPENAI:
            return self._fetch_openai_models()
        elif provider == LLMProvider.GOOGLE:
            return self._fetch_google_models()
        elif provider == LLMProvider.ANTHROPIC:
            return self._fetch_anthropic_models()
        else:
            raise ValueError(f"不支持的供应商: {provider}")

    def _fetch_openai_models(self) -> List[Dict[str, Any]]:
        """获取OpenAI模型列表"""
        try:
            import openai

            api_key = self.get_api_key(LLMProvider.OPENAI)
            if not api_key:
                raise ValueError("OpenAI API密钥未配置")

            client = openai.OpenAI(
                api_key=api_key,
                base_url=os.getenv('OPENAI_BASE_URL', 'https://api.openai.com/v1')
            )

            # 获取模型列表
            models_response = client.models.list()
            models = []

            # 过滤出聊天模型
            chat_models = [
                'gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo', 'gpt-4', 'gpt-3.5-turbo'
            ]

            for model in models_response.data:
                model_id = model.id
                if any(chat_model in model_id for chat_model in chat_models):
                    # 获取模型详细信息
                    max_tokens = self._get_openai_model_max_tokens(model_id)
                    cost = self._get_openai_model_cost(model_id)

                    models.append({
                        "value": model_id,
                        "label": self._format_model_name(model_id),
                        "max_tokens": max_tokens,
                        "cost_per_1k": cost,
                        "streaming": True,
                        "created": model.created,
                        "owned_by": model.owned_by
                    })

            # 按创建时间排序，最新的在前
            models.sort(key=lambda x: x.get('created', 0), reverse=True)
            return models

        except Exception as e:
            logger.error(f"获取OpenAI模型列表失败: {e}")
            raise

    def _fetch_google_models(self) -> List[Dict[str, Any]]:
        """获取Google Gemini模型列表"""
        try:
            import google.generativeai as genai

            api_key = self.get_api_key(LLMProvider.GOOGLE)
            if not api_key:
                raise ValueError("Google API密钥未配置")

            genai.configure(api_key=api_key)

            # 获取模型列表
            models = []
            for model in genai.list_models():
                if 'generateContent' in model.supported_generation_methods:
                    model_name = model.name.replace('models/', '')

                    # 获取模型详细信息
                    max_tokens = self._get_google_model_max_tokens(model_name)
                    cost = self._get_google_model_cost(model_name)

                    models.append({
                        "value": model_name,
                        "label": self._format_model_name(model_name),
                        "max_tokens": max_tokens,
                        "cost_per_1k": cost,
                        "streaming": True,
                        "description": getattr(model, 'description', ''),
                        "version": getattr(model, 'version', '')
                    })

            return models

        except Exception as e:
            logger.error(f"获取Google模型列表失败: {e}")
            raise

    def _fetch_anthropic_models(self) -> List[Dict[str, Any]]:
        """获取Anthropic模型列表"""
        try:
            # Anthropic目前没有公开的模型列表API，使用已知的模型
            api_key = self.get_api_key(LLMProvider.ANTHROPIC)
            if not api_key:
                raise ValueError("Anthropic API密钥未配置")

            # 已知的Claude模型列表
            known_models = [
                {
                    "value": "claude-3-5-sonnet-20241022",
                    "label": "Claude 3.5 Sonnet",
                    "max_tokens": 200000,
                    "cost_per_1k": 0.003,
                    "streaming": True,
                    "description": "最新的Claude 3.5 Sonnet模型"
                },
                {
                    "value": "claude-3-5-haiku-20241022",
                    "label": "Claude 3.5 Haiku",
                    "max_tokens": 200000,
                    "cost_per_1k": 0.00025,
                    "streaming": True,
                    "description": "快速响应的Claude 3.5 Haiku模型"
                },
                {
                    "value": "claude-3-opus-20240229",
                    "label": "Claude 3 Opus",
                    "max_tokens": 200000,
                    "cost_per_1k": 0.015,
                    "streaming": True,
                    "description": "最强大的Claude 3 Opus模型"
                },
                {
                    "value": "claude-3-sonnet-20240229",
                    "label": "Claude 3 Sonnet",
                    "max_tokens": 200000,
                    "cost_per_1k": 0.003,
                    "streaming": True,
                    "description": "平衡性能的Claude 3 Sonnet模型"
                },
                {
                    "value": "claude-3-haiku-20240307",
                    "label": "Claude 3 Haiku",
                    "max_tokens": 200000,
                    "cost_per_1k": 0.00025,
                    "streaming": True,
                    "description": "快速响应的Claude 3 Haiku模型"
                }
            ]

            # 验证模型可用性（可选）
            validated_models = []
            for model in known_models:
                try:
                    # 这里可以添加模型可用性验证逻辑
                    validated_models.append(model)
                except Exception:
                    continue

            return validated_models

        except Exception as e:
            logger.error(f"获取Anthropic模型列表失败: {e}")
            raise

    def _get_openai_model_max_tokens(self, model_id: str) -> int:
        """获取OpenAI模型的最大token数"""
        token_limits = {
            'gpt-4o': 128000,
            'gpt-4o-mini': 128000,
            'gpt-4-turbo': 128000,
            'gpt-4': 8192,
            'gpt-3.5-turbo': 16385,
        }

        for key, limit in token_limits.items():
            if key in model_id:
                return limit

        return 4096  # 默认值

    def _get_openai_model_cost(self, model_id: str) -> float:
        """获取OpenAI模型的成本（每1K tokens）"""
        costs = {
            'gpt-4o': 0.005,
            'gpt-4o-mini': 0.00015,
            'gpt-4-turbo': 0.01,
            'gpt-4': 0.03,
            'gpt-3.5-turbo': 0.0005,
        }

        for key, cost in costs.items():
            if key in model_id:
                return cost

        return 0.001  # 默认值

    def _get_google_model_max_tokens(self, model_name: str) -> int:
        """获取Google模型的最大token数"""
        token_limits = {
            'gemini-1.5-pro': 2097152,
            'gemini-1.5-flash': 1048576,
            'gemini-pro': 32768,
            'gemini-1.0-pro': 32768,
        }

        for key, limit in token_limits.items():
            if key in model_name:
                return limit

        return 32768  # 默认值

    def _get_google_model_cost(self, model_name: str) -> float:
        """获取Google模型的成本（每1K tokens）"""
        costs = {
            'gemini-1.5-pro': 0.00125,
            'gemini-1.5-flash': 0.000075,
            'gemini-pro': 0.0005,
            'gemini-1.0-pro': 0.0005,
        }

        for key, cost in costs.items():
            if key in model_name:
                return cost

        return 0.001  # 默认值

    def _format_model_name(self, model_id: str) -> str:
        """格式化模型名称为用户友好的显示名称"""
        name_mapping = {
            'gpt-4o': 'GPT-4o',
            'gpt-4o-mini': 'GPT-4o Mini',
            'gpt-4-turbo': 'GPT-4 Turbo',
            'gpt-4': 'GPT-4',
            'gpt-3.5-turbo': 'GPT-3.5 Turbo',
            'gemini-1.5-pro': 'Gemini 1.5 Pro',
            'gemini-1.5-flash': 'Gemini 1.5 Flash',
            'gemini-pro': 'Gemini Pro',
            'gemini-1.0-pro': 'Gemini 1.0 Pro',
            'claude-3-5-sonnet': 'Claude 3.5 Sonnet',
            'claude-3-5-haiku': 'Claude 3.5 Haiku',
            'claude-3-opus': 'Claude 3 Opus',
            'claude-3-sonnet': 'Claude 3 Sonnet',
            'claude-3-haiku': 'Claude 3 Haiku',
        }

        # 尝试精确匹配
        for key, name in name_mapping.items():
            if key in model_id.lower():
                return name

        # 如果没有匹配，格式化原始名称
        return model_id.replace('-', ' ').replace('_', ' ').title()

    def clear_model_cache(self, provider: LLMProvider = None):
        """清除模型缓存"""
        if provider:
            cache_key = provider.value
            if cache_key in self._model_cache:
                del self._model_cache[cache_key]
            if cache_key in self._cache_timestamp:
                del self._cache_timestamp[cache_key]
            logger.info(f"已清除{provider.value}的模型缓存")
        else:
            self._model_cache.clear()
            self._cache_timestamp.clear()
            logger.info("已清除所有模型缓存")
    
    def get_provider_display_name(self, provider: LLMProvider) -> str:
        """获取供应商显示名称"""
        names = {
            LLMProvider.OPENAI: "OpenAI",
            LLMProvider.GOOGLE: "Google Gemini",
            LLMProvider.ANTHROPIC: "Anthropic Claude"
        }
        return names.get(provider, provider.value)
    
    def get_api_key(self, provider: LLMProvider) -> Optional[str]:
        """获取指定供应商的API密钥"""
        key_mapping = {
            LLMProvider.OPENAI: "OPENAI_API_KEY",
            LLMProvider.GOOGLE: "GOOGLE_API_KEY",
            LLMProvider.ANTHROPIC: "ANTHROPIC_API_KEY"
        }
        env_key = key_mapping.get(provider)
        if not env_key:
            return None

        api_key = os.getenv(env_key)

        # 检查是否是占位符或无效密钥
        if not api_key or api_key.startswith('your_') or len(api_key) < 10:
            return None

        return api_key
    
    def set_current_config(self, provider: str, model: str):
        """设置当前配置"""
        try:
            self.current_provider = LLMProvider(provider)
            self.current_model = model
            logger.info(f"LLM配置已更新: {provider} - {model}")
        except ValueError:
            logger.error(f"无效的供应商: {provider}")
            raise ValueError(f"不支持的LLM供应商: {provider}")
    
    def get_current_config(self) -> Dict[str, str]:
        """获取当前配置"""
        return {
            "provider": self.current_provider.value,
            "model": self.current_model,
            "provider_name": self.get_provider_display_name(self.current_provider)
        }
    
    def create_llm_instance(self, temperature: float = 0.7, max_tokens: Optional[int] = None):
        """创建LLM实例"""
        try:
            if self.current_provider == LLMProvider.OPENAI:
                return self._create_openai_llm(temperature, max_tokens)
            elif self.current_provider == LLMProvider.GOOGLE:
                return self._create_google_llm(temperature, max_tokens)
            elif self.current_provider == LLMProvider.ANTHROPIC:
                return self._create_anthropic_llm(temperature, max_tokens)
            else:
                raise ValueError(f"不支持的供应商: {self.current_provider}")
        except Exception as e:
            logger.error(f"创建LLM实例失败: {e}")
            raise
    
    def _create_openai_llm(self, temperature: float, max_tokens: Optional[int]):
        """创建OpenAI LLM实例"""
        try:
            from langchain_openai import ChatOpenAI
            
            api_key = self.get_api_key(LLMProvider.OPENAI)
            if not api_key:
                raise ValueError("OpenAI API密钥未配置")
            
            kwargs = {
                "model": self.current_model,
                "temperature": temperature,
                "api_key": api_key
            }
            
            base_url = os.getenv('OPENAI_BASE_URL')
            if base_url:
                kwargs["base_url"] = base_url
            
            if max_tokens:
                kwargs["max_tokens"] = max_tokens
            
            return ChatOpenAI(**kwargs)
            
        except ImportError:
            raise ImportError("请安装 langchain-openai: pip install langchain-openai")
    
    def _create_google_llm(self, temperature: float, max_tokens: Optional[int]):
        """创建Google Gemini LLM实例"""
        try:
            from langchain_google_genai import ChatGoogleGenerativeAI
            
            api_key = self.get_api_key(LLMProvider.GOOGLE)
            if not api_key:
                raise ValueError("Google API密钥未配置")
            
            kwargs = {
                "model": self.current_model,
                "temperature": temperature,
                "google_api_key": api_key
            }
            
            if max_tokens:
                kwargs["max_output_tokens"] = max_tokens
            
            return ChatGoogleGenerativeAI(**kwargs)
            
        except ImportError:
            raise ImportError("请安装 langchain-google-genai: pip install langchain-google-genai")
    
    def _create_anthropic_llm(self, temperature: float, max_tokens: Optional[int]):
        """创建Anthropic Claude LLM实例"""
        try:
            from langchain_anthropic import ChatAnthropic
            
            api_key = self.get_api_key(LLMProvider.ANTHROPIC)
            if not api_key:
                raise ValueError("Anthropic API密钥未配置")
            
            kwargs = {
                "model": self.current_model,
                "temperature": temperature,
                "anthropic_api_key": api_key
            }
            
            if max_tokens:
                kwargs["max_tokens"] = max_tokens
            
            return ChatAnthropic(**kwargs)
            
        except ImportError:
            raise ImportError("请安装 langchain-anthropic: pip install langchain-anthropic")
    
    def test_connection(self, provider: str, model: str) -> Dict[str, Any]:
        """测试LLM连接"""
        try:
            # 临时设置配置
            original_provider = self.current_provider
            original_model = self.current_model
            
            self.set_current_config(provider, model)
            
            # 创建LLM实例并测试
            llm = self.create_llm_instance(temperature=0.1, max_tokens=50)
            
            # 发送测试消息
            from langchain_core.messages import HumanMessage
            test_message = HumanMessage(content="请回复'连接测试成功'")
            response = llm.invoke([test_message])
            
            # 恢复原配置
            self.current_provider = original_provider
            self.current_model = original_model
            
            return {
                "success": True,
                "message": "连接测试成功",
                "response": response.content if hasattr(response, 'content') else str(response)
            }
            
        except Exception as e:
            # 恢复原配置
            self.current_provider = original_provider
            self.current_model = original_model
            
            return {
                "success": False,
                "message": f"连接测试失败: {str(e)}",
                "error": str(e)
            }

# 全局配置管理器实例
llm_config_manager = LLMConfigManager()
