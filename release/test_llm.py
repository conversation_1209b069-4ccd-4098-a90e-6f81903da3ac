#!/usr/bin/env python3
"""
LLM功能测试脚本
测试多供应商LLM配置和智能解析功能
"""

import sys
import os

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.llm_config import llm_config_manager, LLMProvider
from src.release_agent import ReleaseAgent

def test_llm_config_manager():
    """测试LLM配置管理器"""
    print("🧪 测试LLM配置管理器")
    print("=" * 40)
    
    # 测试获取可用供应商
    providers = llm_config_manager.get_available_providers()
    print(f"✅ 可用供应商数量: {len(providers)}")
    
    for provider in providers:
        print(f"  - {provider['label']}: {provider['status']}")
    
    # 测试获取模型列表
    for provider_enum in LLMProvider:
        models = llm_config_manager.get_available_models(provider_enum)
        print(f"✅ {provider_enum.value} 可用模型: {len(models)}")
        for model in models[:2]:  # 只显示前2个
            print(f"  - {model['label']}: {model['max_tokens']:,} tokens, ${model['cost_per_1k']:.6f}/1K")
    
    # 测试当前配置
    current_config = llm_config_manager.get_current_config()
    print(f"✅ 当前配置: {current_config['provider_name']} - {current_config['model']}")

def test_llm_connection():
    """测试LLM连接（如果有API密钥）"""
    print("\n🧪 测试LLM连接")
    print("=" * 40)
    
    # 检查是否有配置的API密钥
    providers_to_test = []
    
    for provider in LLMProvider:
        api_key = llm_config_manager.get_api_key(provider)
        if api_key:
            providers_to_test.append(provider)
            print(f"✅ {provider.value}: API密钥已配置")
        else:
            print(f"⚠️  {provider.value}: API密钥未配置")
    
    if not providers_to_test:
        print("❌ 没有配置任何API密钥，跳过连接测试")
        return
    
    # 测试连接
    for provider in providers_to_test:
        models = llm_config_manager.get_available_models(provider)
        if models:
            test_model = models[0]["value"]  # 使用第一个模型测试
            print(f"\n🔍 测试 {provider.value} - {test_model}")
            
            result = llm_config_manager.test_connection(provider.value, test_model)
            
            if result["success"]:
                print(f"✅ 连接成功: {result['message']}")
                print(f"📝 响应: {result['response'][:100]}...")
            else:
                print(f"❌ 连接失败: {result['message']}")

def test_intelligent_parsing():
    """测试智能解析功能"""
    print("\n🧪 测试智能解析功能")
    print("=" * 40)
    
    # 检查是否有可用的LLM
    current_config = llm_config_manager.get_current_config()
    api_key = llm_config_manager.get_api_key(llm_config_manager.current_provider)
    
    if not api_key:
        print("⚠️  未配置LLM API密钥，只测试正则解析")
        test_regex_only = True
    else:
        print(f"✅ 使用 {current_config['provider_name']} - {current_config['model']} 进行智能解析")
        test_regex_only = False
    
    # 创建Agent实例
    agent = ReleaseAgent()
    
    # 测试用例
    test_cases = [
        # 标准格式（正则可以处理）
        "列出 25R1.2 Prod 的发布计划",
        "查询 25R2.0 Test 环境的部署计划",
        "版本：25R1.5 环境：Staging",
        
        # 复杂格式（需要LLM处理）
        "我想查看25R1.2版本在生产环境的发布计划",
        "帮我看看二十五R一点二在prod环境的部署情况",
        "请显示版本25R2.0在测试环境的发布信息",
    ]
    
    for i, test_input in enumerate(test_cases, 1):
        print(f"\n{i}. 测试输入: '{test_input}'")
        
        # 测试正则解析
        version_regex, env_regex = agent._regex_parse(test_input)
        print(f"   正则解析: 版本={version_regex}, 环境={env_regex}")
        
        # 测试完整解析（包括LLM）
        if not test_regex_only:
            try:
                version_full, env_full = agent.parse_user_input(test_input)
                print(f"   智能解析: 版本={version_full}, 环境={env_full}")
                
                if version_full and env_full and (not version_regex or not env_regex):
                    print("   🎯 LLM智能解析成功补充了正则解析的不足！")
                    
            except Exception as e:
                print(f"   ❌ 智能解析失败: {e}")
        else:
            print("   ⚠️  跳过LLM解析（未配置API密钥）")

def test_model_switching():
    """测试模型切换功能"""
    print("\n🧪 测试模型切换功能")
    print("=" * 40)
    
    # 保存原始配置
    original_config = llm_config_manager.get_current_config()
    print(f"原始配置: {original_config['provider']} - {original_config['model']}")
    
    # 获取可用的供应商和模型
    providers = llm_config_manager.get_available_providers()
    configured_providers = [p for p in providers if p["configured"]]
    
    if len(configured_providers) < 1:
        print("❌ 需要至少配置一个供应商的API密钥")
        return
    
    # 测试切换到不同的模型
    for provider in configured_providers[:2]:  # 最多测试2个供应商
        provider_enum = LLMProvider(provider["value"])
        models = llm_config_manager.get_available_models(provider_enum)
        
        if models:
            test_model = models[0]["value"]
            print(f"\n🔄 切换到: {provider['label']} - {test_model}")
            
            try:
                llm_config_manager.set_current_config(provider["value"], test_model)
                new_config = llm_config_manager.get_current_config()
                print(f"✅ 切换成功: {new_config['provider']} - {new_config['model']}")
                
            except Exception as e:
                print(f"❌ 切换失败: {e}")
    
    # 恢复原始配置
    try:
        llm_config_manager.set_current_config(original_config["provider"], original_config["model"])
        print(f"\n🔄 已恢复原始配置: {original_config['provider']} - {original_config['model']}")
    except Exception as e:
        print(f"❌ 恢复配置失败: {e}")

def main():
    """主测试函数"""
    print("🤖 LLM功能测试")
    print("=" * 50)
    
    try:
        # 基础功能测试
        test_llm_config_manager()
        
        # 连接测试
        test_llm_connection()
        
        # 智能解析测试
        test_intelligent_parsing()
        
        # 模型切换测试
        test_model_switching()
        
        print("\n" + "=" * 50)
        print("🎉 LLM功能测试完成！")
        
        print("\n💡 使用建议:")
        print("1. 配置至少一个LLM供应商的API密钥以启用智能解析")
        print("2. 在Web界面的'🤖 LLM配置'标签页中管理LLM设置")
        print("3. 使用'🧪 测试连接'功能验证配置是否正确")
        print("4. 根据任务需求选择合适的模型和参数")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
