# 🤖 LLM配置指南

## 📋 概述

智能发布计划助手现在支持多个LLM供应商，包括OpenAI、Google Gemini和Anthropic Claude。你可以在Web界面中动态配置和切换不同的LLM供应商和模型。

## 🚀 新功能特性

### 1. 多供应商支持
- **OpenAI**: GPT-4o, GPT-4o Mini, GPT-4 Turbo, GPT-3.5 Turbo
- **Google Gemini**: Gemini 1.5 Pro, Gemini 1.5 Flash, Gemini Pro
- **Anthropic Claude**: Claude 3.5 Sonnet, Claude 3 Opus, Claude 3 Haiku

### 2. 动态配置界面
- 🔧 实时切换LLM供应商和模型
- 🧪 连接测试功能
- ⚙️ 高级参数调整（Temperature、Max Tokens）
- 📊 模型信息展示（成本、Token限制等）

### 3. 智能解析增强
- 📝 使用LLM增强版本号和环境名解析
- 🔄 正则表达式 + LLM双重解析机制
- 🎯 更准确的自然语言理解

## 🔧 配置步骤

### 1. 获取API密钥

#### OpenAI
1. 访问 [OpenAI Platform](https://platform.openai.com/api-keys)
2. 登录你的OpenAI账户
3. 点击 "Create new secret key"
4. 复制生成的API密钥（格式：sk-...）

#### Google Gemini
1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 登录你的Google账户
3. 点击 "Create API Key"
4. 复制生成的API密钥

#### Anthropic Claude
1. 访问 [Anthropic Console](https://console.anthropic.com/)
2. 登录你的Anthropic账户
3. 进入 "API Keys" 页面
4. 创建新的API密钥（格式：sk-ant-...）

### 2. 配置环境变量

编辑 `.env` 文件，添加对应的API密钥：

```env
# OpenAI配置
OPENAI_API_KEY=sk-your-openai-api-key
OPENAI_BASE_URL=https://api.openai.com/v1

# Google Gemini配置
GOOGLE_API_KEY=your-google-api-key

# Anthropic Claude配置
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key

# 默认配置
DEFAULT_LLM_PROVIDER=openai
DEFAULT_LLM_MODEL=gpt-3.5-turbo
```

### 3. 重启应用

```bash
# 停止当前应用（Ctrl+C）
# 重新启动
python run.py
```

## 🌐 Web界面使用

### 1. 访问LLM配置页面
- 启动应用后，访问 http://localhost:8501
- 点击 "🤖 LLM配置" 标签页

### 2. 配置供应商和模型
1. **查看当前配置**: 页面顶部显示当前使用的供应商和模型
2. **选择供应商**: 从已配置API密钥的供应商中选择
3. **选择模型**: 查看模型详情并选择合适的模型
4. **应用配置**: 点击 "🔄 应用配置" 按钮

### 3. 测试连接
- 点击 "🧪 测试连接" 按钮
- 系统会发送测试消息验证配置是否正确
- 查看测试响应确认模型工作正常

### 4. 高级配置
- **Temperature**: 控制输出的创造性（0.0-2.0）
- **Max Tokens**: 限制输出长度
- **自定义测试**: 使用自定义参数测试模型响应

## 📊 模型对比

| 供应商 | 模型 | 最大Token | 每1K Token成本 | 特点 |
|--------|------|-----------|----------------|------|
| OpenAI | GPT-4o | 128K | $0.005 | 最新多模态模型 |
| OpenAI | GPT-4o Mini | 128K | $0.00015 | 高性价比选择 |
| OpenAI | GPT-3.5 Turbo | 16K | $0.0005 | 经典选择 |
| Google | Gemini 1.5 Pro | 2M | $0.00125 | 超长上下文 |
| Google | Gemini 1.5 Flash | 1M | $0.000075 | 最高性价比 |
| Anthropic | Claude 3.5 Sonnet | 200K | $0.003 | 推理能力强 |
| Anthropic | Claude 3 Haiku | 200K | $0.00025 | 快速响应 |

## 🎯 使用建议

### 1. 模型选择建议
- **日常使用**: Gemini 1.5 Flash（性价比最高）
- **复杂任务**: Claude 3.5 Sonnet（推理能力强）
- **长文本处理**: Gemini 1.5 Pro（2M上下文）
- **快速响应**: GPT-4o Mini（速度快）

### 2. 参数调整建议
- **Temperature**:
  - 0.0-0.3: 确定性任务（解析、分类）
  - 0.4-0.7: 平衡创造性和准确性
  - 0.8-1.0: 创造性任务
- **Max Tokens**:
  - 解析任务: 100-500
  - 对话任务: 500-1000
  - 长文本生成: 1000+

### 3. 成本控制
- 优先使用性价比高的模型
- 合理设置Max Tokens限制
- 监控API使用量

## 🔍 故障排除

### 1. API密钥问题
- **错误**: "API密钥未配置"
- **解决**: 检查.env文件中对应的API密钥是否正确设置

### 2. 连接测试失败
- **错误**: "连接测试失败"
- **解决**: 
  - 检查网络连接
  - 验证API密钥是否有效
  - 确认账户余额是否充足

### 3. 模型响应异常
- **错误**: 解析结果不准确
- **解决**:
  - 调整Temperature参数
  - 尝试不同的模型
  - 检查输入格式

### 4. 导入错误
- **错误**: "请安装 langchain-xxx"
- **解决**: 
  ```bash
  pip install -r requirements.txt --upgrade
  ```

## 🚀 智能解析功能

### 1. 双重解析机制
1. **正则表达式解析**: 快速匹配标准格式
2. **LLM智能解析**: 处理复杂或非标准输入

### 2. 支持的输入格式
- 标准格式: "25R1.2 Prod"
- 中文格式: "版本：25R1.2 环境：生产"
- 自然语言: "我想查看25R1.2在生产环境的发布计划"
- 复杂描述: "帮我看看二十五R一点二版本在prod环境的部署情况"

### 3. 使用示例
```
输入: "请帮我查询25R1.2版本在生产环境的发布计划"
正则解析: 失败
LLM解析: 成功 → 版本=25R1.2, 环境=Prod
```

## 📈 性能优化

### 1. 缓存机制
- 相同输入的解析结果会被缓存
- 减少重复的LLM调用

### 2. 降级策略
- LLM不可用时自动降级到正则解析
- 确保基本功能始终可用

### 3. 异步处理
- LLM调用采用异步方式
- 不阻塞主要业务流程

## 🔮 未来计划

1. **更多供应商支持**: 阿里云、百度等国内供应商
2. **本地模型支持**: Ollama、LocalAI等本地部署方案
3. **智能推荐**: 根据任务类型自动推荐最适合的模型
4. **成本分析**: 详细的API使用成本统计和分析
5. **批量处理**: 支持批量任务的智能调度

---

**更新日期**: 2024年12月30日  
**版本**: v1.0.0  
**状态**: ✅ 已验证可用
