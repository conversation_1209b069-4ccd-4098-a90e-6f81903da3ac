# 🚀 快速启动指南

## 📋 前置条件

- Python 3.8+
- MySQL数据库（可选，用于完整功能）
- Jenkins服务器（可选，用于完整功能）

## ⚡ 快速体验（无需配置）

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行演示
```bash
# 查看核心功能演示
python demo.py
```

### 3. 运行测试
```bash
# 运行功能测试
python test_agent.py
```

## 🔧 完整配置（生产环境）

### 1. 环境配置
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

### 2. 配置内容示例
```env
# 数据库配置
MYSQL_HOST=your-mysql-host
MYSQL_PORT=3306
MYSQL_USER=your-username
MYSQL_PASSWORD=your-password
MYSQL_DATABASE=release_info

# Jenkins配置
JENKINS_URL=http://your-jenkins-server:8080
JENKINS_USERNAME=your-jenkins-username
JENKINS_TOKEN=your-jenkins-token
```

### 3. 启动Web界面
```bash
# 使用启动脚本（推荐）
python run.py

# 或直接使用Streamlit
streamlit run app.py
```

## 🌐 Web界面使用

### 访问地址
- 本地访问: http://localhost:8501
- 网络访问: http://0.0.0.0:8501

### 功能标签页

1. **📝 发布计划查询**
   - 输入版本号（如：25R1.2）
   - 选择环境（Prod/Staging/Test/Dev）
   - 点击"查询发布计划"

2. **✅ 计划审批**
   - 审查发布计划
   - 选择"审批通过"或"拒绝"
   - 确认要执行的Jenkins jobs

3. **🚀 执行管理**
   - 监控job执行状态
   - 查看执行结果
   - 刷新状态信息

4. **📊 监控面板**
   - 输入Jenkins job名称和build号
   - 查看实时执行状态
   - 获取控制台输出

## 💡 使用示例

### 查询发布计划
```
输入: 25R1.2
环境: Prod
结果: 显示完整的发布计划表格和Jenkins job列表
```

### 支持的输入格式
- "列出 25R1.2 Prod 的发布计划"
- "查询 25R2.0 Test 环境的部署计划"
- "环境：Staging 版本：25R1.5"

## 🔍 故障排除

### 常见问题

1. **依赖安装失败**
   ```bash
   # 使用虚拟环境
   python -m venv venv
   source venv/bin/activate  # Linux/Mac
   # 或
   venv\Scripts\activate     # Windows
   pip install -r requirements.txt
   ```

2. **数据库连接失败**
   - 检查MySQL服务是否运行
   - 验证.env文件中的数据库配置
   - 确认数据库用户权限

3. **Jenkins连接失败**
   - 检查Jenkins URL是否正确
   - 验证用户名和API Token
   - 确认Jenkins API权限

4. **Web界面无法访问**
   ```bash
   # 检查端口是否被占用
   lsof -i :8501
   
   # 使用不同端口启动
   streamlit run app.py --server.port 8502
   ```

### 日志查看
- 应用日志会显示在终端
- 可以调整日志级别获取更多信息

## 📚 更多信息

- 详细文档: [README.md](README.md)
- 项目总结: [PROJECT_SUMMARY.md](PROJECT_SUMMARY.md)
- 功能演示: `python demo.py`
- 功能测试: `python test_agent.py`

## 🆘 获取帮助

如果遇到问题：
1. 查看错误日志
2. 检查配置文件
3. 运行测试脚本诊断
4. 参考故障排除指南

---

🎉 **恭喜！您已成功部署智能发布计划助手！**
