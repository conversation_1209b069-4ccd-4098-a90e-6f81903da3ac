#!/usr/bin/env python3
"""
启动脚本
用于启动Streamlit应用
"""

import subprocess
import sys
import os
from pathlib import Path

def check_requirements():
    """检查依赖是否安装"""
    try:
        import streamlit
        import langchain
        import langgraph
        import mysql.connector
        import jenkins
        print("✅ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def check_env_file():
    """检查环境配置文件"""
    env_file = Path(".env")
    if not env_file.exists():
        print("⚠️  .env文件不存在，请复制.env.example并配置相关参数")
        return False
    
    print("✅ .env文件存在")
    return True

def main():
    """主函数"""
    print("🚀 智能发布计划助手启动器")
    print("=" * 50)
    
    # 检查依赖
    if not check_requirements():
        sys.exit(1)
    
    # 检查环境配置
    if not check_env_file():
        print("继续启动，但某些功能可能不可用...")
    
    # 启动Streamlit应用
    print("\n🌐 启动Web界面...")
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "app.py",
            "--server.port", "8501",
            "--server.address", "0.0.0.0"
        ])
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
