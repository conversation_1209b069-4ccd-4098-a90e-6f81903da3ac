#!/usr/bin/env python3
"""
实时模型获取功能演示脚本
展示如何使用新的实时模型获取功能
"""

import sys
import os
import time

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.llm_config import llm_config_manager, LLMProvider

def demo_header():
    """显示演示标题"""
    print("🔄 实时模型获取功能演示")
    print("=" * 50)
    print("本演示展示如何从LLM供应商实时获取最新的模型列表")
    print()

def demo_provider_status():
    """演示供应商状态检查"""
    print("🔍 检查供应商配置状态")
    print("-" * 30)
    
    providers = llm_config_manager.get_available_providers()
    
    for provider in providers:
        status_icon = "✅" if provider["configured"] else "❌"
        print(f"{status_icon} {provider['label']}: {provider['status']}")
    
    configured_count = sum(1 for p in providers if p["configured"])
    print(f"\n📊 已配置供应商: {configured_count}/{len(providers)}")
    
    if configured_count == 0:
        print("\n💡 提示: 请在.env文件中配置至少一个供应商的API密钥")
        print("   参考: LLM_CONFIG_GUIDE.md")
    
    print()

def demo_realtime_fetching():
    """演示实时模型获取"""
    print("📡 实时模型获取演示")
    print("-" * 30)
    
    # 找到已配置的供应商
    providers = llm_config_manager.get_available_providers()
    configured_providers = [p for p in providers if p["configured"]]
    
    if not configured_providers:
        print("⚠️  没有配置任何供应商，使用模拟演示")
        demo_simulated_fetching()
        return
    
    for provider_info in configured_providers[:2]:  # 最多演示2个供应商
        provider = LLMProvider(provider_info["value"])
        print(f"\n🔍 获取 {provider_info['label']} 的模型列表...")
        
        try:
            # 清除缓存，强制实时获取
            llm_config_manager.clear_model_cache(provider)
            
            start_time = time.time()
            models = llm_config_manager.get_available_models(provider, use_cache=False)
            duration = time.time() - start_time
            
            if models:
                print(f"✅ 成功获取 {len(models)} 个模型 (耗时: {duration:.2f}秒)")
                
                # 显示前3个模型
                for i, model in enumerate(models[:3]):
                    print(f"  {i+1}. {model['label']}")
                    print(f"     📋 ID: {model['value']}")
                    print(f"     🔢 Token: {model['max_tokens']:,}")
                    print(f"     💰 成本: ${model['cost_per_1k']:.6f}/1K")
                
                if len(models) > 3:
                    print(f"  ... 还有 {len(models) - 3} 个模型")
            else:
                print("❌ 未获取到任何模型")
                
        except Exception as e:
            print(f"❌ 获取失败: {e}")
    
    print()

def demo_simulated_fetching():
    """模拟演示（当没有配置API密钥时）"""
    print("🎭 模拟演示模式")
    print("-" * 20)
    
    # 模拟OpenAI模型获取
    print("📡 正在获取 OpenAI 模型列表...")
    time.sleep(1)  # 模拟网络延迟
    
    simulated_models = [
        {"label": "GPT-4o", "value": "gpt-4o", "max_tokens": 128000, "cost_per_1k": 0.005},
        {"label": "GPT-4o Mini", "value": "gpt-4o-mini", "max_tokens": 128000, "cost_per_1k": 0.00015},
        {"label": "GPT-4 Turbo", "value": "gpt-4-turbo", "max_tokens": 128000, "cost_per_1k": 0.01},
    ]
    
    print(f"✅ 成功获取 {len(simulated_models)} 个模型 (模拟)")
    
    for i, model in enumerate(simulated_models):
        print(f"  {i+1}. {model['label']}")
        print(f"     📋 ID: {model['value']}")
        print(f"     🔢 Token: {model['max_tokens']:,}")
        print(f"     💰 成本: ${model['cost_per_1k']:.6f}/1K")

def demo_cache_mechanism():
    """演示缓存机制"""
    print("💾 缓存机制演示")
    print("-" * 30)
    
    # 找一个已配置的供应商
    providers = llm_config_manager.get_available_providers()
    configured_providers = [p for p in providers if p["configured"]]
    
    if not configured_providers:
        print("⚠️  没有配置供应商，演示缓存概念")
        print("🔄 第一次获取: 从API实时获取 (较慢)")
        print("💾 第二次获取: 使用缓存数据 (很快)")
        print("⏰ 缓存过期: 5分钟后自动过期")
        print("🔄 手动刷新: 可以强制清除缓存")
        print()
        return
    
    provider = LLMProvider(configured_providers[0]["value"])
    provider_name = configured_providers[0]["label"]
    
    print(f"🔍 使用 {provider_name} 演示缓存机制")
    
    # 清除缓存
    llm_config_manager.clear_model_cache(provider)
    print("🗑️  已清除缓存")
    
    # 第一次获取（实时）
    print("\n📡 第一次获取（实时）...")
    start_time = time.time()
    try:
        models1 = llm_config_manager.get_available_models(provider, use_cache=False)
        duration1 = time.time() - start_time
        print(f"✅ 获取 {len(models1)} 个模型，耗时: {duration1:.2f}秒")
    except Exception as e:
        print(f"❌ 获取失败: {e}")
        return
    
    # 第二次获取（缓存）
    print("\n💾 第二次获取（使用缓存）...")
    start_time = time.time()
    models2 = llm_config_manager.get_available_models(provider, use_cache=True)
    duration2 = time.time() - start_time
    print(f"✅ 获取 {len(models2)} 个模型，耗时: {duration2:.2f}秒")
    
    # 性能对比
    if duration1 > 0 and duration2 > 0:
        speedup = duration1 / duration2
        print(f"⚡ 缓存加速: {speedup:.1f}x 倍")
    
    print(f"⏰ 缓存有效期: {llm_config_manager.CACHE_DURATION}秒")
    print()

def demo_model_comparison():
    """演示模型对比功能"""
    print("📊 模型对比演示")
    print("-" * 30)
    
    all_models = {}
    total_models = 0
    
    # 收集所有已配置供应商的模型
    providers = llm_config_manager.get_available_providers()
    for provider_info in providers:
        if not provider_info["configured"]:
            continue
        
        try:
            provider = LLMProvider(provider_info["value"])
            models = llm_config_manager.get_available_models(provider)
            all_models[provider_info["label"]] = models
            total_models += len(models)
            print(f"✅ {provider_info['label']}: {len(models)} 个模型")
        except Exception as e:
            print(f"❌ {provider_info['label']}: 获取失败")
    
    if not all_models:
        print("⚠️  没有成功获取任何模型，使用示例数据演示")
        # 使用示例数据
        all_models = {
            "OpenAI": [
                {"label": "GPT-4o", "cost_per_1k": 0.005, "max_tokens": 128000},
                {"label": "GPT-3.5 Turbo", "cost_per_1k": 0.0005, "max_tokens": 16385},
            ],
            "Google Gemini": [
                {"label": "Gemini 1.5 Pro", "cost_per_1k": 0.00125, "max_tokens": 2097152},
                {"label": "Gemini 1.5 Flash", "cost_per_1k": 0.000075, "max_tokens": 1048576},
            ]
        }
        total_models = 4
    
    print(f"\n📈 总计: {total_models} 个模型")
    
    # 成本分析
    print("\n💰 成本分析:")
    all_costs = []
    for provider_name, models in all_models.items():
        costs = [m['cost_per_1k'] for m in models]
        if costs:
            min_cost = min(costs)
            max_cost = max(costs)
            print(f"  {provider_name}: ${min_cost:.6f} - ${max_cost:.6f}")
            all_costs.extend(costs)
    
    if all_costs:
        print(f"  🏆 最低成本: ${min(all_costs):.6f}/1K")
        print(f"  💎 最高成本: ${max(all_costs):.6f}/1K")
    
    # Token分析
    print("\n🔢 Token限制分析:")
    all_tokens = []
    for provider_name, models in all_models.items():
        tokens = [m['max_tokens'] for m in models]
        if tokens:
            max_token = max(tokens)
            print(f"  {provider_name}: 最大 {max_token:,} tokens")
            all_tokens.extend(tokens)
    
    if all_tokens:
        print(f"  🚀 全局最大: {max(all_tokens):,} tokens")
    
    print()

def demo_web_interface_features():
    """演示Web界面功能"""
    print("🌐 Web界面功能演示")
    print("-" * 30)
    
    print("📱 在Web界面中，你可以:")
    print("  1. 🔄 点击'刷新模型'按钮获取最新模型列表")
    print("  2. 📊 使用'模型列表管理'查看所有供应商统计")
    print("  3. 💾 在'缓存管理'中控制缓存状态")
    print("  4. 🔧 实时切换供应商和模型")
    print("  5. 🧪 测试模型连接和响应")
    
    print("\n🎯 主要优势:")
    print("  ✅ 始终获取最新模型")
    print("  ✅ 智能缓存提升性能")
    print("  ✅ 详细的模型信息")
    print("  ✅ 友好的用户界面")
    print("  ✅ 完善的错误处理")
    
    print("\n🚀 访问Web界面:")
    print("  1. 运行: python run.py")
    print("  2. 访问: http://localhost:8501")
    print("  3. 点击: '🤖 LLM配置' 标签页")
    print()

def main():
    """主演示函数"""
    demo_header()
    
    # 检查供应商状态
    demo_provider_status()
    
    # 实时模型获取演示
    demo_realtime_fetching()
    
    # 缓存机制演示
    demo_cache_mechanism()
    
    # 模型对比演示
    demo_model_comparison()
    
    # Web界面功能介绍
    demo_web_interface_features()
    
    print("🎉 实时模型获取功能演示完成！")
    print("\n💡 下一步:")
    print("1. 配置API密钥以体验完整功能")
    print("2. 启动Web界面: python run.py")
    print("3. 查看详细指南: REALTIME_MODELS_GUIDE.md")

if __name__ == "__main__":
    main()
