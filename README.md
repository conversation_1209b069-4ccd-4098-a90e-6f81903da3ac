# 智能发布计划助手

基于LangChain和LangGraph的智能发布计划管理系统，用于自动化软件发布流程。

## 🚀 功能特性

- **智能发布计划查询**: 基于版本号和环境名自动查询数据库获取发布计划
- **可视化计划展示**: 以表格形式清晰展示多租户和单租户服务的部署计划
- **Jenkins集成**: 自动生成Jenkins job列表并支持一键触发
- **工作流管理**: 使用LangGraph设计的完整工作流，支持用户审批和确认
- **实时监控**: 持续监控Jenkins job执行状态和控制台输出
- **Web界面**: 基于Streamlit的简洁易用的Web界面

## 📋 系统架构

```
智能发布计划助手
├── 数据库查询模块 (database.py)
│   ├── MySQL连接管理
│   ├── 多租户服务查询
│   └── 单租户部署计划查询
├── Jenkins集成模块 (jenkins_client.py)
│   ├── Job触发和参数传递
│   ├── 执行状态监控
│   └── 控制台输出获取
├── LangGraph工作流 (agent_workflow.py)
│   ├── 获取部署计划
│   ├── 用户审批流程
│   ├── 执行确认
│   └── 监控执行
├── Agent核心逻辑 (release_agent.py)
│   ├── 用户输入解析
│   ├── 发布计划格式化
│   └── 状态管理
└── Web界面 (app.py)
    ├── 发布计划查询
    ├── 计划审批
    ├── 执行管理
    └── 监控面板
```

## 🛠️ 安装和配置

### 1. 环境要求

- Python 3.8+
- MySQL数据库
- Jenkins服务器

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 环境配置

复制环境配置文件并填写相关信息：

```bash
cp .env.example .env
```

编辑`.env`文件：

```env
# 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=your_username
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=release_info

# Jenkins配置
JENKINS_URL=http://your-jenkins-server:8080
JENKINS_USERNAME=your_jenkins_username
JENKINS_TOKEN=your_jenkins_token

# LLM配置 (可选)
OPENAI_API_KEY=your_openai_api_key
```

### 4. 数据库准备

确保MySQL数据库中包含以下表结构：
- `release_version`: 发布版本信息
- `release_service`: 发布服务关联
- `service`: 服务信息
- `environment`: 环境信息
- `deployment_plan_view`: 部署计划视图

## 🚀 使用方法

### 1. 启动应用

```bash
python run.py
```

或直接使用Streamlit：

```bash
streamlit run app.py
```

### 2. 功能测试

运行测试脚本验证功能：

```bash
python test_agent.py
```

### 3. Web界面使用

1. **发布计划查询**: 输入版本号和环境，查询发布计划
2. **计划审批**: 审查发布计划并决定是否继续
3. **执行管理**: 选择要执行的Jenkins jobs并监控执行
4. **监控面板**: 实时查看Jenkins job执行状态和日志

## 📊 工作流程

```mermaid
graph TD
    A[用户输入版本号和环境] --> B[查询数据库获取发布计划]
    B --> C[生成Jenkins job列表]
    C --> D[用户审批发布计划]
    D -->|审批通过| E[用户确认执行]
    D -->|拒绝| F[结束流程]
    E -->|确认| G[触发Jenkins jobs]
    E -->|取消| F
    G --> H[监控执行状态]
    H --> I[获取控制台输出]
    I --> J[检查是否完成]
    J -->|未完成| H
    J -->|完成| K[流程结束]
```

## 🔧 支持的服务

系统支持以下服务的Jenkins job自动生成：

- **aries**: Aries-Deploy
- **canis**: Canis-K8S
- **em**: ProdCsmc
- **openlog**: Prod-K8S-Tracing
- **pisces**: IaC-terraform-SageMaker
- **chinacrm**: 租户特定job
- **lumos**: lumos-k8s-deployment
- **taurus**: Prod-Taurus
- **rigel**: Prod-Rigel
- **hydra**: Hydra-Single-Tenant-Deploy-Increment
- **mintaka**: Prod-Mintaka

## 📝 配置说明

### Jenkins Job参数映射

每个服务都有预定义的Jenkins job配置，包括：
- Job名称
- 必需参数
- 默认值
- 参数格式转换规则

### 版本号格式

- 输入格式: `25R1.2`
- Release分支: `release/251.2`
- Release标签: `LR/251.2.0` (或 `GR/252.0.0` 对于主版本)

## 🐛 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否运行
   - 验证数据库连接参数
   - 确认数据库用户权限

2. **Jenkins连接失败**
   - 检查Jenkins URL是否正确
   - 验证用户名和Token
   - 确认Jenkins API权限

3. **依赖安装问题**
   - 使用虚拟环境
   - 更新pip版本
   - 检查Python版本兼容性

### 日志查看

应用使用Python logging模块记录详细日志，可以通过调整日志级别获取更多调试信息。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📄 许可证

本项目采用MIT许可证。

## 📞 支持

如有问题或建议，请通过以下方式联系：
- 创建GitHub Issue
- 发送邮件至项目维护者

---

**注意**: 这是一个原型验证系统，在生产环境使用前请进行充分测试。
