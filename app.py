"""
Streamlit Web界面
使用Streamlit创建简单的web界面供用户交互
"""

import streamlit as st
import pandas as pd
import time
import logging
from typing import Dict, Any, List
import sys
import os

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.release_agent import ReleaseAgent

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 页面配置
st.set_page_config(
    page_title="智能发布计划助手",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 初始化session state
if 'agent' not in st.session_state:
    st.session_state.agent = ReleaseAgent()
if 'deployment_plan' not in st.session_state:
    st.session_state.deployment_plan = None
if 'jenkins_jobs' not in st.session_state:
    st.session_state.jenkins_jobs = None
if 'execution_status' not in st.session_state:
    st.session_state.execution_status = None
if 'monitoring_jobs' not in st.session_state:
    st.session_state.monitoring_jobs = {}

def main():
    """主函数"""
    st.title("🚀 智能发布计划助手")
    st.markdown("基于LangChain和LangGraph的发布计划管理系统")
    
    # 侧边栏
    with st.sidebar:
        st.header("📋 操作面板")
        
        # 环境配置检查
        st.subheader("🔧 环境配置")
        check_environment_config()
        
        st.divider()
        
        # 快速操作
        st.subheader("⚡ 快速操作")
        if st.button("🔄 重置会话", type="secondary"):
            reset_session()
            st.rerun()
    
    # 主要内容区域
    tab1, tab2, tab3, tab4 = st.tabs(["📝 发布计划查询", "✅ 计划审批", "🚀 执行管理", "📊 监控面板"])
    
    with tab1:
        deployment_plan_tab()
    
    with tab2:
        plan_approval_tab()
    
    with tab3:
        execution_management_tab()
    
    with tab4:
        monitoring_dashboard_tab()

def check_environment_config():
    """检查环境配置"""
    config_items = [
        ("数据库", ["MYSQL_HOST", "MYSQL_USER", "MYSQL_PASSWORD"]),
        ("Jenkins", ["JENKINS_URL", "JENKINS_USERNAME", "JENKINS_TOKEN"])
    ]
    
    for name, env_vars in config_items:
        all_configured = all(os.getenv(var) for var in env_vars)
        status = "✅" if all_configured else "❌"
        st.write(f"{status} {name}")

def reset_session():
    """重置会话状态"""
    keys_to_reset = ['deployment_plan', 'jenkins_jobs', 'execution_status', 'monitoring_jobs']
    for key in keys_to_reset:
        if key in st.session_state:
            del st.session_state[key]
    st.session_state.agent = ReleaseAgent()

def deployment_plan_tab():
    """发布计划查询标签页"""
    st.header("📝 发布计划查询")
    
    # 输入区域
    col1, col2 = st.columns(2)
    
    with col1:
        version = st.text_input(
            "版本号",
            placeholder="例如: 25R1.2",
            help="输入发布版本号，格式如 25R1.2"
        )
    
    with col2:
        environment = st.selectbox(
            "环境",
            ["Prod", "Staging", "Test", "Dev"],
            help="选择目标环境"
        )
    
    # 查询按钮
    if st.button("🔍 查询发布计划", type="primary"):
        if version and environment:
            with st.spinner("正在查询发布计划..."):
                query_deployment_plan(version, environment)
        else:
            st.error("请输入版本号和选择环境")
    
    # 显示查询结果
    if st.session_state.deployment_plan:
        display_deployment_plan()

def query_deployment_plan(version: str, environment: str):
    """查询发布计划"""
    try:
        user_input = f"列出 {version} {environment} 的发布计划"
        result = st.session_state.agent.process_user_request(user_input)
        
        if result["success"]:
            st.session_state.deployment_plan = result["data"]
            st.success(result["message"])
        else:
            st.error(result["message"])
            
    except Exception as e:
        st.error(f"查询失败: {str(e)}")

def display_deployment_plan():
    """显示发布计划"""
    data = st.session_state.deployment_plan
    
    st.subheader(f"📋 {data['version']} {data['environment']}环境 发布计划")
    
    # 显示发布计划表格
    if "deployment_plan" in data:
        st.markdown(data["deployment_plan"])
    
    # 显示Jenkins jobs
    if "jenkins_jobs" in data:
        st.subheader("🔧 Jenkins Jobs")
        st.markdown(data["jenkins_jobs"])

def plan_approval_tab():
    """计划审批标签页"""
    st.header("✅ 计划审批")
    
    if not st.session_state.deployment_plan:
        st.info("请先在「发布计划查询」标签页查询发布计划")
        return
    
    data = st.session_state.deployment_plan
    workflow_state = data.get("workflow_state", "")
    
    if workflow_state == "waiting_plan_approval":
        st.warning("⏳ 等待审批发布计划")
        
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("✅ 审批通过", type="primary"):
                approve_plan()
        
        with col2:
            if st.button("❌ 拒绝", type="secondary"):
                reject_plan()
    
    elif workflow_state == "waiting_execution_approval":
        st.warning("⏳ 等待确认执行")
        display_jenkins_jobs_selection()
    
    else:
        st.info(f"当前状态: {workflow_state}")

def approve_plan():
    """审批发布计划"""
    try:
        result = st.session_state.agent.approve_deployment_plan()
        if result["success"]:
            st.success(result["message"])
            st.session_state.deployment_plan.update(result["data"])
            st.rerun()
        else:
            st.error(result["message"])
    except Exception as e:
        st.error(f"审批失败: {str(e)}")

def reject_plan():
    """拒绝发布计划"""
    reason = st.text_input("拒绝原因", placeholder="请输入拒绝原因...")
    if st.button("确认拒绝"):
        try:
            result = st.session_state.agent.reject_deployment_plan(reason)
            if result["success"]:
                st.success(result["message"])
                st.session_state.deployment_plan.update(result["data"])
                st.rerun()
            else:
                st.error(result["message"])
        except Exception as e:
            st.error(f"拒绝失败: {str(e)}")

def display_jenkins_jobs_selection():
    """显示Jenkins jobs选择"""
    st.subheader("🔧 选择要执行的Jenkins Jobs")
    
    if not st.session_state.agent.current_state or not st.session_state.agent.current_state.jenkins_jobs:
        st.error("没有可用的Jenkins jobs")
        return
    
    jobs = st.session_state.agent.current_state.jenkins_jobs
    
    # 创建选择框
    selected_indices = []
    for i, job in enumerate(jobs):
        selected = st.checkbox(
            f"{job['job_name']} - {job['customer_name']} ({job['service_name']})",
            value=True,
            key=f"job_{i}"
        )
        if selected:
            selected_indices.append(i)
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("🚀 确认执行", type="primary"):
            approve_execution(selected_indices)
    
    with col2:
        if st.button("❌ 取消执行", type="secondary"):
            reject_execution()

def approve_execution(selected_indices: List[int]):
    """确认执行"""
    try:
        result = st.session_state.agent.approve_execution(selected_indices)
        if result["success"]:
            st.success(result["message"])
            st.session_state.execution_status = result["data"]
            st.rerun()
        else:
            st.error(result["message"])
    except Exception as e:
        st.error(f"执行确认失败: {str(e)}")

def reject_execution():
    """拒绝执行"""
    reason = st.text_input("取消原因", placeholder="请输入取消原因...")
    if st.button("确认取消"):
        try:
            result = st.session_state.agent.reject_execution(reason)
            if result["success"]:
                st.success(result["message"])
                st.rerun()
            else:
                st.error(result["message"])
        except Exception as e:
            st.error(f"取消失败: {str(e)}")

def execution_management_tab():
    """执行管理标签页"""
    st.header("🚀 执行管理")
    
    # 获取执行状态
    if st.button("🔄 刷新状态"):
        refresh_execution_status()
    
    if st.session_state.execution_status:
        display_execution_status()
    else:
        st.info("没有正在执行的任务")

def refresh_execution_status():
    """刷新执行状态"""
    try:
        result = st.session_state.agent.get_execution_status()
        if result["success"]:
            st.session_state.execution_status = result["data"]
        else:
            st.error(result["message"])
    except Exception as e:
        st.error(f"刷新状态失败: {str(e)}")

def display_execution_status():
    """显示执行状态"""
    data = st.session_state.execution_status
    
    # 状态概览
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("当前状态", data.get("workflow_state", "未知"))
    
    with col2:
        st.metric("当前Job索引", data.get("current_job_index", 0))
    
    with col3:
        completed = "是" if data.get("execution_completed", False) else "否"
        st.metric("执行完成", completed)
    
    # 执行结果
    if data.get("job_results"):
        st.subheader("📊 执行结果")
        for i, result in enumerate(data["job_results"]):
            with st.expander(f"Job {i+1}: {result.get('job_name', '未知')}"):
                st.json(result)
    
    # 消息和错误
    if data.get("messages"):
        st.subheader("📝 消息")
        for msg in data["messages"]:
            st.info(msg)
    
    if data.get("errors"):
        st.subheader("❌ 错误")
        for error in data["errors"]:
            st.error(error)

def monitoring_dashboard_tab():
    """监控面板标签页"""
    st.header("📊 监控面板")
    
    # Jenkins Job监控
    st.subheader("🔍 Jenkins Job监控")
    
    col1, col2 = st.columns(2)
    
    with col1:
        job_name = st.text_input("Job名称", placeholder="输入Jenkins job名称")
    
    with col2:
        build_number = st.number_input("Build号", min_value=1, value=1)
    
    if st.button("🔍 监控Job"):
        if job_name:
            monitor_jenkins_job(job_name, build_number)
        else:
            st.error("请输入Job名称")
    
    # 显示监控结果
    if st.session_state.monitoring_jobs:
        display_monitoring_results()

def monitor_jenkins_job(job_name: str, build_number: int):
    """监控Jenkins job"""
    try:
        result = st.session_state.agent.monitor_jenkins_job(job_name, build_number)
        if result["success"]:
            st.session_state.monitoring_jobs[f"{job_name}_{build_number}"] = result["data"]
            st.success(f"开始监控 {job_name} Build {build_number}")
        else:
            st.error(result["message"])
    except Exception as e:
        st.error(f"监控失败: {str(e)}")

def display_monitoring_results():
    """显示监控结果"""
    st.subheader("📈 监控结果")
    
    for job_key, data in st.session_state.monitoring_jobs.items():
        with st.expander(f"📊 {job_key}"):
            
            # 状态信息
            if "status" in data:
                status_info = data["status"]
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.metric("状态", status_info.get("status", "未知"))
                
                with col2:
                    building = "是" if status_info.get("building", False) else "否"
                    st.metric("正在构建", building)
                
                with col3:
                    duration = status_info.get("duration", 0) / 1000  # 转换为秒
                    st.metric("持续时间(秒)", f"{duration:.1f}")
            
            # 控制台输出
            if "console_output" in data and data["console_output"]:
                st.subheader("📝 控制台输出")
                st.code(data["console_output"], language="text")

if __name__ == "__main__":
    main()
